import 'package:espot/models/teams_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/ui/pages/data_teams_input_page.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:espot/ui/widgets/data_teams_item.dart';

class DataTeamsPage extends StatefulWidget with CacheManager {
  const DataTeamsPage({super.key});

  @override
  State<DataTeamsPage> createState() => _DataTeamsPageState();
}

class _DataTeamsPageState extends State<DataTeamsPage> {
  final searchController = TextEditingController(text: '');
  final DatabaseReference _dbRef =
      FirebaseDatabase.instance.ref().child('teams');

  TeamsModel? selectedTeams;
  String searchResult = '';
  List<TeamsModel> teamsList = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Load teams data from Firebase
    fetchTeams();
  }

  // Fetch teams data from Firebase
  void fetchTeams() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Get snapshot from Firebase
      final DataSnapshot snapshot = await _dbRef.get();

      if (snapshot.exists) {
        // Clear current list
        teamsList.clear();

        // Convert snapshot to map
        final data = snapshot.value as Map<dynamic, dynamic>;

        // Loop through each team and add to list
        data.forEach((key, value) {
          final TeamsModel team = TeamsModel.fromMap(value, key);
          teamsList.add(team);
        });
      }
    } catch (e) {
      CustomSnackBar.showToast(
          context, 'Error fetching teams: ${e.toString()}');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // Delete team
  void deleteTeam() async {
    if (selectedTeams == null || selectedTeams!.uid == null) {
      return;
    }

    try {
      // Delete from Firebase
      await _dbRef.child(selectedTeams!.uid!).remove();

      // Remove from list and reset selection
      setState(() {
        teamsList.removeWhere((team) => team.uid == selectedTeams!.uid);
        selectedTeams = null;
      });

      // Show success message
      Navigator.pushNamed(context, '/data-teams-success-delete');
    } catch (e) {
      CustomSnackBar.showToast(context, 'Error deleting team: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Teams',
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, '/teams-input');
            },
            icon: const Icon(Icons.add),
            iconSize: 30,
          ),
          IconButton(
            onPressed: () {
              Navigator.pushNamedAndRemoveUntil(
                  context, '/home', (route) => false);
            },
            icon: const Icon(Icons.home),
            iconSize: 30,
          )
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              // physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
              ),
              children: [
                const SizedBox(
                  height: 40,
                ),
                Row(
                  children: [
                    Text(
                      'List Teams',
                      style: blackTextStyle.copyWith(
                        fontSize: 16,
                        fontWeight: semiBold,
                      ),
                    ),
                    const Spacer(),
                    selectedTeams != null
                        ? Row(
                            children: [
                              GestureDetector(
                                child: const Icon(Icons.edit),
                                onTapUp: (details) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => DataTeamsInputPage(
                                        data: selectedTeams!,
                                      ),
                                    ),
                                  ).then((_) => fetchTeams());
                                },
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                child: const Icon(Icons.delete),
                                onTapUp: (details) {
                                  EasyLoading.show(status: 'loading...');
                                  deleteTeam();
                                  EasyLoading.dismiss();
                                },
                              ),
                            ],
                          )
                        : Container(),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                teamsList.isEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 50.0),
                          child: Text(
                            'No teams found',
                            style: blackTextStyle.copyWith(
                              fontSize: 16,
                              fontWeight: medium,
                            ),
                          ),
                        ),
                      )
                    : ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: teamsList.length,
                        itemBuilder: (context, index) {
                          TeamsModel dataTeams = teamsList[index];
                          return GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedTeams = dataTeams;
                                });
                              },
                              child: DataTeamsItem(
                                dataTeams: dataTeams,
                                isSelected: selectedTeams != null
                                    ? selectedTeams!.uid == dataTeams.uid
                                    : false,
                              ));
                        },
                      ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
    );
  }
}
